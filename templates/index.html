<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时邮箱 - 免费一次性邮箱服务</title>
    <meta name="description" content="免费临时邮箱服务，保护您的隐私。创建自动过期的一次性邮箱地址。">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="/static/apple-touch-icon.png">
    <link rel="apple-touch-icon-precomposed" href="/static/apple-touch-icon-precomposed.png">
    <style>
        .email-box {
            font-family: monospace;
            letter-spacing: 0.05em;
        }

        /* 默认单栏样式 */
        .inbox-section {
            display: block;
        }

        /* 两栏模式样式 - 只在激活时生效 */
        .inbox-section.two-column-mode {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 1rem;
        }

        .message-list {
            /* 默认不设置特殊样式 */
        }

        /* 两栏模式下的邮件列表样式 */
        .two-column-mode .message-list {
            max-height: calc(100vh - 350px);
            overflow-y: auto;
            border-right: 1px solid #e5e7eb;
            padding-right: 1rem;
        }

        .message-content-area {
            display: none;
        }

        /* 两栏模式下显示内容区域 */
        .two-column-mode .message-content-area {
            display: block;
            max-height: calc(100vh - 350px);
            overflow-y: auto;
            padding-left: 1rem;
        }

        .message-preview {
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .message-preview:hover {
            background-color: #f3f4f6;
        }

        .message-preview.active {
            background-color: #e5e7eb;
            border-left: 3px solid #3b82f6;
        }

        .message-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 300px;
            color: #9ca3af;
        }

        /* 关闭按钮 */
        .close-btn {
            display: none;
        }

        .two-column-mode .close-btn {
            display: inline-block;
        }

        /* 移动端适配 - 基础样式保留 */
        @media (max-width: 767px) {
            .inbox-section.two-column-mode {
                display: block;
            }

            .two-column-mode .message-list {
                border-right: none;
                padding-right: 0;
                margin-bottom: 1rem;
            }

            .two-column-mode .message-content-area {
                padding-left: 0;
                border-top: 1px solid #e5e7eb;
                padding-top: 1rem;
            }
        }

        /* 移动端按钮组优化 - 已移至外部CSS文件 */
        /* 这些样式现在在 static/css/styles.css 中定义 */
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <header class="bg-blue-600 text-white shadow-md">
        <div class="container mx-auto px-4 py-4">
            <div class="header-content flex justify-between items-center">
                <div class="header-brand flex items-center">
                    <i class="fas fa-envelope text-2xl mr-2"></i>
                    <h1 class="text-xl font-bold" data-i18n="nav.title">临时邮箱</h1>
                </div>
                <nav class="header-nav">
                    <ul class="nav-list flex space-x-6 items-center">
                        <li class="nav-item"><a href="#" class="nav-link hover:underline" data-i18n="nav.home">首页</a></li>
                        <li class="nav-item"><a href="#" class="nav-link hover:underline" data-i18n="nav.faq">常见问题</a></li>
                        <li class="nav-item"><a href="#" class="nav-link hover:underline" data-i18n="nav.contact">联系我们</a></li>
                        <!-- 语言切换器 -->
                        <li class="nav-item language-selector-item relative">
                            <select id="language-selector" class="language-selector bg-blue-500 text-white border border-blue-400 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-300">
                                <option value="zh-CN">中文</option>
                                <option value="en-US">English</option>
                            </select>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-6" data-i18n="main.title">您的临时邮箱地址</h2>

            <div class="mb-8">
                <!-- 邮箱地址显示区域 -->
                <div class="email-box bg-gray-100 p-4 rounded-lg mb-4 long-press-hint haptic-feedback">
                    <span id="email-address" class="text-blue-600 font-bold break-all"></span>
                </div>

                <!-- 操作按钮组 -->
                <div class="button-group">
                    <button id="copy-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-copy mr-2"></i><span data-i18n="button.copy">复制</span>
                    </button>
                    <button id="refresh-btn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-sync-alt mr-2"></i><span data-i18n="button.refresh">刷新</span>
                    </button>
                    <button id="new-email-btn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-plus mr-2"></i><span data-i18n="button.new_email">新邮箱</span>
                    </button>
                    <button id="custom-email-btn" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-edit mr-2"></i><span data-i18n="button.custom">自定义</span>
                    </button>
                    <button id="history-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-history mr-2"></i><span data-i18n="button.history">历史记录</span>
                    </button>
                    <button id="delete-email-btn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 haptic-feedback min-h-[44px] flex items-center justify-center col-span-2 sm:col-span-1">
                        <i class="fas fa-trash mr-2"></i><span data-i18n="button.delete_reset">删除邮箱</span>
                    </button>
                </div>
            </div>

            <div class="border-t pt-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold text-gray-800" data-i18n="inbox.title">收件箱</h3>
                    <button id="close-email-btn" class="close-btn text-gray-500 hover:text-gray-700 px-3 py-1 rounded transition-colors">
                        <i class="fas fa-times mr-1"></i><span data-i18n="button.close">关闭</span>
                    </button>
                </div>

                <div id="inbox-section" class="inbox-section">
                    <!-- Message List with Pull-to-Refresh -->
                    <div class="message-list pull-to-refresh scroll-smooth">
                        <!-- Pull-to-Refresh Indicator -->
                        <div class="pull-to-refresh-indicator">
                            <i class="fas fa-arrow-down text-blue-500"></i>
                        </div>

                        <div id="inbox" class="space-y-2"></div>
                        <div id="no-messages" class="text-center text-gray-500 py-8">
                            <i class="fas fa-inbox text-4xl mb-4"></i>
                            <p data-i18n="inbox.no_messages">暂无邮件</p>
                            <p class="text-sm" data-i18n="inbox.no_messages_desc">收到邮件时会显示在这里</p>
                        </div>
                    </div>

                    <!-- Message Content Area (hidden by default) -->
                    <div id="message-content-area" class="message-content-area scroll-smooth">
                        <div class="message-placeholder">
                            <div class="text-center">
                                <i class="fas fa-envelope-open text-4xl mb-4"></i>
                                <p data-i18n="inbox.select_message">选择邮件查看内容</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 自定义前缀输入弹窗 -->
    <div id="custom-prefix-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mobile-modal-content">
                <div class="flex justify-between items-center p-4 border-b">
                    <h3 class="text-lg font-semibold text-gray-800" data-i18n="custom.title">自定义邮箱前缀</h3>
                    <button id="close-custom-modal" class="text-gray-400 hover:text-gray-600 haptic-feedback min-h-[44px] min-w-[44px] flex items-center justify-center">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="p-4">
                    <div class="mb-4">
                        <label for="custom-prefix-input" class="block text-sm font-medium text-gray-700 mb-2" data-i18n="custom.label">
                            邮箱前缀（1-20字符，支持字母、数字、连字符）
                        </label>
                        <input
                            type="text"
                            id="custom-prefix-input"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="例如：myemail123"
                            maxlength="20"
                            autocomplete="off"
                            autocapitalize="off"
                            spellcheck="false"
                        >
                        <div id="prefix-error" class="text-red-500 text-sm mt-1 hidden"></div>
                        <div class="text-gray-500 text-xs mt-1" data-i18n="custom.hint">
                            提示：前缀将用于生成您的临时邮箱地址
                        </div>
                    </div>
                    <div class="flex gap-3">
                        <button id="cancel-custom-btn" class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px]" data-i18n="button.cancel">
                            取消
                        </button>
                        <button id="confirm-custom-btn" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px]" data-i18n="button.confirm">
                            确认
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史邮箱选择弹窗 -->
    <div id="history-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mobile-modal-content">
                <div class="flex justify-between items-center p-4 border-b">
                    <h3 class="text-lg font-semibold text-gray-800" data-i18n="history.title">邮箱历史记录</h3>
                    <button id="close-history-modal" class="text-gray-400 hover:text-gray-600 haptic-feedback min-h-[44px] min-w-[44px] flex items-center justify-center">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="p-4">
                    <div id="history-loading" class="text-center py-8 hidden">
                        <i class="fas fa-spinner fa-spin text-2xl text-blue-500 mb-2"></i>
                        <p class="text-gray-600" data-i18n="history.loading">加载历史记录...</p>
                    </div>
                    <div id="history-empty" class="text-center py-8 hidden">
                        <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600" data-i18n="history.empty">暂无历史记录</p>
                        <p class="text-sm text-gray-500" data-i18n="history.empty_desc">生成邮箱后会显示在这里</p>
                    </div>
                    <div id="history-list" class="space-y-2 max-h-64 overflow-y-auto scroll-smooth">
                        <!-- 历史邮箱列表将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between">
                <div class="mb-6 md:mb-0">
                    <h3 class="text-lg font-semibold mb-2" data-i18n="footer.title">临时邮箱</h3>
                    <p class="text-gray-400" data-i18n="footer.description">免费一次性临时邮箱服务</p>
                </div>
                <div class="grid grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-sm font-semibold uppercase tracking-wider mb-4" data-i18n="footer.links">链接</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors" data-i18n="nav.home">首页</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors" data-i18n="nav.faq">常见问题</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold uppercase tracking-wider mb-4" data-i18n="footer.legal">法律</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors" data-i18n="footer.terms">服务条款</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors" data-i18n="footer.privacy">隐私政策</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400 text-sm">
                <p data-i18n="footer.copyright">© 2025 临时邮箱. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script>
        // 将服务器配置传递给JavaScript
        window.appConfig = {
            AUTO_REFRESH_ENABLED: {{ config.AUTO_REFRESH_ENABLED|tojson }},
            AUTO_REFRESH_INTERVAL: {{ config.AUTO_REFRESH_INTERVAL|tojson }},
            ENV: {{ config.ENV|tojson }},
            API_BASE_URL: {{ config.API_BASE_URL|tojson }},
            API_TIMEOUT: {{ config.API_TIMEOUT|tojson }},
            API_RETRY_ATTEMPTS: {{ config.API_RETRY_ATTEMPTS|tojson }},
            API_RETRY_DELAY: {{ config.API_RETRY_DELAY|tojson }}
        };
        console.log("App config loaded:", window.appConfig);
    </script>
    <!-- 引入国际化模块 -->
    <script src="{{ url_for('static', filename='js/i18n.js') }}"></script>
    <!-- 引入API配置模块 -->
    <script src="{{ url_for('static', filename='js/api-config.js') }}"></script>
    <!-- 引入移动端增强模块 -->
    <script src="{{ url_for('static', filename='js/mobile.js') }}"></script>
    <!-- 引入主应用脚本 -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>