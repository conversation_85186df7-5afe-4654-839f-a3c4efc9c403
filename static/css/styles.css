/* 加载状态动画 */
.loading-pulse {
    position: relative;
    opacity: 0.7;
    animation: pulse 1.5s ease-in-out infinite;
}

.loading-fade {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.error-state {
    color: #dc3545;
    transition: color 0.3s ease;
}

@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 0.4; }
    100% { opacity: 0.7; }
}

/* 确保加载状态时文本可读 */
.loading-pulse, .loading-fade {
    cursor: wait;
}

/* 调整 Font Awesome fa-spin 动画速度 */
.fa-spin {
  -webkit-animation: fa-spin 1s infinite linear;
  animation: fa-spin 1s infinite linear;
}

/* 在HTML的<style>标签中添加这个样式 */
.email-item.active {
    background-color: #e5e7eb;
    border-left: 3px solid #3b82f6;
}

/* 刷新按钮宽度统一样式 */
#refresh-btn {
    min-width: 100px; /* 设置刷新按钮最小宽度与新邮箱按钮一致 */
}

/* ===== 移动端响应式适配样式 ===== */

/* 基础触摸优化 */
* {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    -webkit-touch-callout: none;
}

/* 按钮触摸区域优化 */
button, .btn, .button {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* ===== 按钮组默认样式（桌面端） ===== */
.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
    align-items: center;
}

.button-group button {
    flex: 0 0 auto;
    margin: 0;
}

/* 输入框触摸优化 */
input, select, textarea {
    font-size: 16px; /* 防止iOS缩放 */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* 滚动优化 */
.scroll-smooth {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* 移动端模态框优化 */
.mobile-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.mobile-modal-content {
    background: white;
    border-radius: 12px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* 下拉刷新指示器 */
.pull-to-refresh {
    position: relative;
    overflow: hidden;
}

.pull-to-refresh-indicator {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.pull-to-refresh.pulling .pull-to-refresh-indicator {
    top: 10px;
}

.pull-to-refresh.refreshing .pull-to-refresh-indicator {
    top: 10px;
    background: rgba(59, 130, 246, 0.2);
}

/* 滑动操作样式 */
.swipe-item {
    position: relative;
    overflow: hidden;
    touch-action: pan-y;
}

.swipe-actions {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    display: flex;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.swipe-item.swiped .swipe-actions {
    transform: translateX(0);
}

.swipe-action {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.swipe-action.delete {
    background-color: #ef4444;
}

.swipe-action.delete:active {
    background-color: #dc2626;
}

.swipe-action.mark {
    background-color: #3b82f6;
}

.swipe-action.mark:active {
    background-color: #2563eb;
}

/* 触觉反馈类 */
.haptic-feedback {
    transition: transform 0.1s ease;
}

.haptic-feedback:active {
    transform: scale(0.98);
}

/* 长按复制提示 */
.long-press-hint {
    position: relative;
}

.long-press-hint::after {
    content: '长按复制';
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.long-press-hint.show-hint::after {
    opacity: 1;
}

/* ===== 响应式媒体查询 ===== */

/* ===== 移动端按钮组响应式布局 ===== */

/* 移动端按钮样式重置 - 完全无边框图标按钮风格 */
@media (max-width: 768px) {
    .button-group button {
        background: transparent !important;
        border: none !important;
        color: #6b7280 !important; /* 中性灰色文字 */
        transition: all 0.2s ease !important;
        box-shadow: none !important;
        padding: 0.75rem 0.5rem !important;
        border-radius: 8px !important;
    }

    .button-group button:hover {
        background: rgba(107, 114, 128, 0.1) !important; /* 淡灰色悬停背景 */
        color: #374151 !important; /* 深灰色文字 */
        transform: translateY(-1px) !important;
    }

    .button-group button:active {
        transform: translateY(0) scale(0.98) !important;
        background: rgba(107, 114, 128, 0.15) !important;
    }

    .button-group button:disabled {
        background: transparent !important;
        color: #d1d5db !important;
        transform: none !important;
        opacity: 0.5 !important;
    }

    /* 移动端按钮图标和文字优化 */
    .button-group button i {
        font-size: 1.1em !important;
        margin-right: 0.5rem !important;
        opacity: 0.8 !important;
    }

    .button-group button span {
        font-weight: 500 !important;
        font-size: 0.875rem !important;
    }

    .button-group button:hover i {
        opacity: 1 !important;
    }
}

/* 小型手机 (320px-375px) - 横向滚动布局 */
@media (max-width: 375px) {
    /* 按钮组布局优化 - 改为横向滚动 */
    .button-group {
        display: flex !important;
        flex-direction: row !important;
        gap: 0.5rem;
        margin-top: 1rem;
        overflow-x: auto !important;
        overflow-y: hidden !important;
        padding: 0.5rem 0 !important;
        -webkit-overflow-scrolling: touch !important;
        scrollbar-width: none !important; /* Firefox */
        -ms-overflow-style: none !important; /* IE/Edge */
    }

    /* 隐藏滚动条 */
    .button-group::-webkit-scrollbar {
        display: none !important;
    }

    .button-group button {
        flex: 0 0 auto !important;
        min-width: 80px !important;
        padding: 0.75rem 0.75rem !important;
        margin: 0;
        white-space: nowrap !important;
        font-size: 0.8rem !important;
    }

    .button-group button i {
        font-size: 1em !important;
        margin-right: 0.375rem !important;
    }

    .button-group button span {
        font-size: 0.8rem !important;
    }

    /* 容器和间距调整 */
    .container {
        padding: 0.75rem;
    }

    /* 标题字体大小调整 */
    h1 {
        font-size: 1.5rem;
    }

    h2 {
        font-size: 1.25rem;
    }

    /* 邮箱地址显示优化 */
    .email-box {
        font-size: 0.875rem;
        padding: 0.75rem;
        word-break: break-all;
    }

    /* 邮件列表项优化 */
    .email-item {
        padding: 0.75rem;
        font-size: 0.875rem;
    }

    /* 模态框优化 */
    .mobile-modal {
        padding: 0.5rem;
    }

    .mobile-modal-content {
        max-width: 95vw;
        max-height: 85vh;
    }


}

/* 标准手机 (376px-428px) - 横向布局 */
@media (min-width: 376px) and (max-width: 428px) {
    /* 按钮组布局 - 横向排列 */
    .button-group {
        display: flex !important;
        flex-direction: row !important;
        flex-wrap: wrap !important;
        gap: 0.5rem;
        margin-top: 1rem;
        justify-content: flex-start !important;
    }

    .button-group button {
        flex: 0 0 auto !important;
        min-width: 85px !important;
        padding: 0.75rem 0.75rem !important;
        margin: 0;
        font-size: 0.875rem !important;
    }

    .button-group button i {
        font-size: 1em !important;
        margin-right: 0.375rem !important;
    }

    .button-group button span {
        font-size: 0.875rem !important;
    }

    /* 邮箱地址显示 */
    .email-box {
        font-size: 0.9rem;
        padding: 1rem;
    }

    /* 邮件列表优化 */
    .email-item {
        padding: 1rem;
    }
}

/* 小型平板 (429px-768px) - 横向布局 */
@media (min-width: 429px) and (max-width: 768px) {
    /* 按钮组布局 - 横向排列 */
    .button-group {
        display: flex !important;
        flex-direction: row !important;
        flex-wrap: wrap !important;
        gap: 0.75rem;
        margin-top: 1rem;
        justify-content: flex-start !important;
    }

    .button-group button {
        flex: 0 0 auto !important;
        min-width: 100px !important;
        padding: 0.75rem 1rem !important;
        margin: 0;
        font-size: 0.875rem !important;
    }

    .button-group button i {
        font-size: 1.1em !important;
        margin-right: 0.5rem !important;
    }

    .button-group button span {
        font-size: 0.875rem !important;
    }

    /* 两栏布局在此尺寸下保持垂直堆叠 */
    .inbox-section.two-column-mode {
        display: block;
    }

    .two-column-mode .message-list {
        border-right: none;
        padding-right: 0;
        margin-bottom: 1rem;
        max-height: 300px;
    }

    .two-column-mode .message-content-area {
        padding-left: 0;
        border-top: 1px solid #e5e7eb;
        padding-top: 1rem;
        max-height: 400px;
    }
}

/* 大型平板 (769px-1024px) - 恢复水平布局 */
@media (min-width: 769px) and (max-width: 1024px) {
    /* 按钮组布局 - 水平排列 */
    .button-group {
        display: flex !important;
        flex-wrap: wrap;
        gap: 0.75rem;
        margin-top: 1rem;
        align-items: center;
    }

    .button-group button {
        flex: 0 0 auto;
        margin: 0;
    }

    /* 恢复两栏布局 */
    .inbox-section.two-column-mode {
        display: grid;
        grid-template-columns: 1fr 1.5fr;
        gap: 1.5rem;
    }

    .two-column-mode .message-list {
        border-right: 1px solid #e5e7eb;
        padding-right: 1rem;
        margin-bottom: 0;
        max-height: calc(100vh - 300px);
    }

    .two-column-mode .message-content-area {
        padding-left: 1rem;
        border-top: none;
        padding-top: 0;
        max-height: calc(100vh - 300px);
    }
}

/* 桌面端 (1025px以上) - 确保水平布局 */
@media (min-width: 1025px) {
    .button-group {
        display: flex !important;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 1rem;
        align-items: center;
    }

    .button-group button {
        flex: 0 0 auto;
        margin: 0;
    }
}

/* 通用移动端优化 (768px以下) */
@media (max-width: 768px) {
    /* 页面整体布局 */
    body {
        padding: 0;
    }

    main {
        padding: 1rem;
    }

    /* 卡片容器 */
    .max-w-6xl {
        max-width: 100%;
        margin: 0;
        border-radius: 0.5rem;
    }



    /* 邮箱地址区域 */
    .email-box {
        margin-bottom: 1rem;
    }

    /* 按钮间距优化 */
    .actions {
        margin-top: 1rem;
    }

    /* 收件箱标题区域 */
    .border-t {
        padding-top: 1.5rem;
    }

    /* 邮件列表滚动优化 */
    .message-list {
        -webkit-overflow-scrolling: touch;
    }

    .message-content-area {
        -webkit-overflow-scrolling: touch;
    }

    /* 历史记录弹窗优化 */
    #history-modal .max-w-md {
        max-width: 95vw;
        margin: 1rem;
    }

    #history-modal .max-h-96 {
        max-height: 80vh;
    }

    /* 页脚优化 */
    footer .flex {
        flex-direction: column;
        gap: 2rem;
    }

    footer .grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

/* ===== 移动端特定样式增强 ===== */

/* 触摸状态样式 */
.email-item.touching {
    background-color: #f3f4f6;
    transform: scale(0.98);
}

/* 移动端按钮组细节优化 */
@media (max-width: 640px) {
    .button-group button {
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
        border-radius: 8px !important;
    }
}

@media (max-width: 480px) {
    .button-group button {
        font-size: 0.9rem;
        padding: 0.875rem 1rem;
        border-radius: 10px !important;
    }
}

/* 移动端按钮组滚动指示器 */
@media (max-width: 375px) {
    .button-group::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 20px;
        height: 100%;
        background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8));
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .button-group.scrollable::after {
        opacity: 1;
    }

    .button-group {
        position: relative;
    }
}

/* 移动端按钮图标优化 */
@media (max-width: 768px) {
    .button-group button i {
        font-size: 1.1em;
        margin-right: 0.5rem;
    }

    .button-group button span {
        font-weight: 500;
    }
}

/* 邮箱地址区域移动端优化 */
@media (max-width: 768px) {
    .email-box {
        font-size: 0.875rem;
        line-height: 1.4;
        padding: 1rem;
        min-height: 60px;
        display: flex;
        align-items: center;
    }

    .email-box span {
        word-break: break-all;
        overflow-wrap: break-word;
    }
}

/* 模态框移动端优化 */
@media (max-width: 768px) {
    .mobile-modal {
        padding: 1rem;
        align-items: flex-end;
    }

    .mobile-modal-content {
        max-width: 100%;
        max-height: 90vh;
        border-radius: 12px 12px 0 0;
        margin: 0;
        width: 100%;
    }

    /* 自定义前缀输入框优化 */
    #custom-prefix-input {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 1rem;
        border-radius: 8px;
    }
}

/* 下拉刷新在不同屏幕尺寸的优化 */
@media (max-width: 768px) {
    .pull-to-refresh-indicator {
        width: 36px;
        height: 36px;
        top: -50px;
    }

    .pull-to-refresh.pulling .pull-to-refresh-indicator,
    .pull-to-refresh.refreshing .pull-to-refresh-indicator {
        top: 15px;
    }
}

/* 滑动操作在小屏幕上的优化 */
@media (max-width: 480px) {
    .swipe-action {
        min-width: 70px;
        font-size: 16px;
    }
}

/* 长按提示在移动端的优化 */
@media (max-width: 768px) {
    .long-press-hint::after {
        bottom: -30px;
        font-size: 11px;
        padding: 6px 10px;
        border-radius: 6px;
    }
}

/* 邮件列表项在移动端的优化 */
@media (max-width: 768px) {
    .email-item {
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 0.5rem;
        border: 1px solid #e5e7eb;
        border-bottom: 1px solid #e5e7eb;
    }

    .email-item:last-child {
        border-bottom: 1px solid #e5e7eb;
    }

    .email-item.active {
        border-left: 4px solid #3b82f6;
        background-color: #eff6ff;
    }
}

/* 收件箱区域移动端优化 */
@media (max-width: 768px) {
    .message-list {
        max-height: calc(100vh - 400px);
        min-height: 300px;
    }

    .two-column-mode .message-content-area {
        max-height: calc(100vh - 200px);
        min-height: 200px;
    }
}

/* ===== 导航栏响应式样式 ===== */

/* 导航栏基础样式 */
.header-content {
    position: relative;
}

.header-brand {
    flex-shrink: 0;
}

.header-nav {
    flex-shrink: 1;
    min-width: 0;
}

.nav-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.nav-item {
    flex-shrink: 0;
}

.nav-link {
    display: block;
    padding: 0.5rem 0;
    white-space: nowrap;
    transition: color 0.2s ease;
}

.language-selector-item {
    margin-left: auto;
}

.language-selector {
    min-width: 80px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.language-selector:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 桌面端导航栏 (>768px) */
@media (min-width: 769px) {
    .header-content {
        flex-direction: row;
        align-items: center;
    }

    .nav-list {
        flex-direction: row;
        gap: 2rem;
        justify-content: flex-end;
    }

    .language-selector-item {
        margin-left: 1rem;
    }
}

/* 平板端导航栏 (429px-768px) */
@media (min-width: 429px) and (max-width: 768px) {
    .header-content {
        flex-direction: row;
        align-items: center;
        gap: 1rem;
    }

    .nav-list {
        flex-direction: row;
        gap: 1rem;
        justify-content: flex-end;
        flex-wrap: wrap;
    }

    .nav-link {
        font-size: 0.875rem;
        padding: 0.25rem 0;
    }

    .language-selector {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
        min-width: 70px;
    }
}

/* 移动端导航栏 (≤428px) */
@media (max-width: 428px) {
    .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .header-brand {
        align-self: center;
    }

    .header-brand h1 {
        font-size: 1.25rem;
    }

    .nav-list {
        flex-direction: row;
        justify-content: center;
        align-items: center;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .nav-item {
        flex: 0 0 auto;
    }

    .nav-link {
        font-size: 0.875rem;
        padding: 0.5rem 0.25rem;
        text-align: center;
    }

    .language-selector-item {
        margin-left: 0;
        order: 1;
        flex: 0 0 auto;
    }

    .language-selector {
        font-size: 0.75rem;
        padding: 0.5rem;
        min-width: 65px;
        border-radius: 6px;
    }
}

/* 超小屏幕导航栏 (≤375px) */
@media (max-width: 375px) {
    .header-content {
        gap: 0.75rem;
    }

    .header-brand h1 {
        font-size: 1.125rem;
    }

    .nav-list {
        gap: 0.5rem;
        justify-content: space-between;
    }

    .nav-link {
        font-size: 0.8rem;
        padding: 0.375rem 0.125rem;
    }

    .language-selector {
        font-size: 0.7rem;
        padding: 0.375rem 0.25rem;
        min-width: 60px;
    }
}

/* 导航栏移动端优化 - 保持向后兼容 */
@media (max-width: 640px) {
    header .container {
        padding: 0.75rem 1rem;
    }
}

/* 页脚移动端优化 */
@media (max-width: 768px) {
    footer {
        padding: 2rem 0;
        margin-top: 2rem;
    }

    footer .container {
        padding: 0 1rem;
    }

    footer h3, footer h4 {
        font-size: 1rem;
    }

    footer p, footer a {
        font-size: 0.875rem;
    }
}

/* 错误消息移动端优化 */
@media (max-width: 768px) {
    #error-message {
        margin: 1rem 0;
        padding: 1rem;
        font-size: 0.875rem;
        border-radius: 8px;
    }
}

/* 加载状态移动端优化 */
@media (max-width: 768px) {
    .loading-pulse {
        animation: pulse 1.2s ease-in-out infinite;
    }
}

/* 性能优化 - 减少动画在低端设备上的影响 */
@media (prefers-reduced-motion: reduce) {
    .haptic-feedback,
    .email-item,
    .pull-to-refresh-indicator,
    .swipe-actions {
        transition: none;
        animation: none;
    }

    .loading-pulse {
        animation: none;
        opacity: 0.7;
    }
}
